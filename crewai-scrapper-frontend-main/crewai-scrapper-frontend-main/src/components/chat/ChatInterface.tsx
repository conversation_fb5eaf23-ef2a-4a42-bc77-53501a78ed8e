import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../api/store";
import {
  closeChat,
  setTyping,
  addMessage,
  setCurrentConversation,
  setCurrentMessages,
  setLastSearchTerm,
  setSearching,
} from "../../api/services/Chat/ChatSlice";
import {
  useCreateConversationMutation,
  useAddMessageMutation,
  useChatIntegratedSearchMutation,
  useGetConversationQuery,
} from "../../api/services/Chat/ChatService";
import { X, Send, MessageCircle, Loader2, Lightbulb } from "lucide-react";
import MessageList from "./MessageList";
import ConversationList from "./ConversationList";
import { useChatSearch } from "../../hooks/useChatSearch";
import {
  extractSearchIntent,
  enhanceSearchQuery,
  generateSearchSuggestions,
  SearchContext
} from "../../utils/searchEnhancement";

interface ChatInterfaceProps {
  onProductsUpdate?: (products: any[], searchTerm: string) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onProductsUpdate }) => {
  const dispatch = useDispatch();
  const {
    isChatOpen,
    activeConversationId,
    currentConversation,
    currentMessages = [], // Default to empty array
    isTyping,
    isSearching,
    chatWidth,
    lastSearchTerm,
  } = useSelector((state: RootState) => state.chat || {});

  const [inputMessage, setInputMessage] = useState("");
  const [showConversations, setShowConversations] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Ensure currentMessages is always an array
  const safeCurrentMessages = Array.isArray(currentMessages) ? currentMessages : [];

  const { performChatSearch } = useChatSearch();

  const [createConversation, { isLoading: isCreating }] = useCreateConversationMutation();
  const [addMessage, { isLoading: isAddingMessage }] = useAddMessageMutation();
  const [chatSearch, { isLoading: isChatSearching }] = useChatIntegratedSearchMutation();

  // Get conversation data if we have an active conversation
  const { data: conversationData } = useGetConversationQuery(
    activeConversationId || "",
    { skip: !activeConversationId }
  );

  // Update local state when conversation data changes
  useEffect(() => {
    if (conversationData) {
      console.log(`[Frontend] Loading conversation data:`, conversationData);
      console.log(`[Frontend] Messages in conversation:`, conversationData.messages?.length || 0);
      dispatch(setCurrentConversation(conversationData));
      dispatch(setCurrentMessages(conversationData.messages || []));
    }
  }, [conversationData, dispatch]);

  // Show loading state when fetching conversation
  const isLoadingConversation = activeConversationId && !conversationData && !currentConversation;

  // Get current search term from URL as fallback
  const urlParams = new URLSearchParams(window.location.search);
  const currentSearchTerm = lastSearchTerm || urlParams.get('search') || urlParams.get('original') || 'your products';

  // Focus input when chat opens
  useEffect(() => {
    if (isChatOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isChatOpen]);

  // Generate search suggestions when input changes
  useEffect(() => {
    if (inputMessage.trim().length > 2) {
      const context: SearchContext = {
        previousSearches: safeCurrentMessages
          .filter(msg => msg.searchTermGenerated)
          .map(msg => msg.searchTermGenerated!)
          .slice(-3),
        userPreferences: {},
        conversationHistory: safeCurrentMessages.map(msg => ({
          role: msg.role,
          content: msg.content,
          searchTerm: msg.searchTermGenerated
        }))
      };

      const suggestions = generateSearchSuggestions(inputMessage, context);
      setSearchSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } else {
      setShowSuggestions(false);
      setSearchSuggestions([]);
    }
  }, [inputMessage, safeCurrentMessages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isCreating || isAddingMessage) return;

    const messageText = inputMessage.trim();
    setInputMessage("");
    setShowSuggestions(false);

    try {
      if (!activeConversationId) {
        // Create new conversation if none exists
        const result = await createConversation({ initialMessage: messageText }).unwrap();

        dispatch(setCurrentConversation(result.conversation));
        dispatch(setCurrentMessages(result.messages));
        dispatch(setLastSearchTerm(result.searchTerm));

        // Trigger product search with the AI-generated search term
        if (result.searchTerm && onProductsUpdate) {
          // Show searching state in chat
          dispatch(setSearching(true));
          onProductsUpdate([], result.searchTerm); // Pass empty array to trigger new search
          // setSearching(false) will be called by the parent component after search completes
        }
      } else {
        // Add message to existing conversation
        console.log(`[Frontend] Adding message to existing conversation ${activeConversationId}:`, messageText);
        console.log(`[Frontend] Current messages count:`, safeCurrentMessages.length);

        const result = await addMessage({
          conversationId: activeConversationId,
          message: messageText,
        }).unwrap();

        console.log(`[Frontend] Received response:`, result);
        console.log(`[Frontend] New search term:`, result.searchTerm);

        dispatch(addMessage(result.messages[0])); // User message
        dispatch(addMessage(result.messages[1])); // AI response
        dispatch(setLastSearchTerm(result.searchTerm));

        // Trigger product search with the new AI-generated search term
        if (result.searchTerm && onProductsUpdate) {
          // Show searching state in chat
          dispatch(setSearching(true));
          onProductsUpdate([], result.searchTerm); // Pass empty array to trigger new search
          // setSearching(false) will be called by the parent component after search completes
        }
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isChatOpen) {
    return null;
  }

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Minimized Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
            <MessageCircle className="h-3 w-3 text-white" />
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">AI Shopping Assistant</span>
            <div className="text-xs text-gray-500">Always ready to help</div>
          </div>
        </div>
        <button
          onClick={() => dispatch(closeChat())}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title="Minimize Assistant"
        >
          <X className="h-3 w-3 text-gray-500" />
        </button>
      </div>

      {/* AI Status and Recommendations */}
      <div className="flex-1 flex flex-col p-3 space-y-3">
        {/* Current AI Status */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <div className="flex-1">
              <p className="text-sm text-blue-800 leading-relaxed">
                {isSearching || isChatSearching ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin inline mr-1" />
                    I will search for sourcing options for {currentSearchTerm} for you. Please hold on.
                  </>
                ) : currentSearchTerm && currentSearchTerm !== 'your products' ? (
                  `I have displayed the product search results for ${currentSearchTerm}. If you need further assistance or have other specific product requests, feel free to let me know!`
                ) : (
                  "👋 Hi! I'm your AI shopping assistant. Search for any product above and I'll help you find the best sourcing options with smart recommendations."
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Product Search Status */}
        {currentSearchTerm && currentSearchTerm !== 'your products' && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <span className="text-xs font-medium text-gray-600">Product search</span>
            </div>
            <p className="text-sm text-gray-800 font-medium">{currentSearchTerm}</p>
          </div>
        )}

        {/* AI Search Recommendations */}
        {currentSearchTerm && currentSearchTerm !== 'your products' && (
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-3">
              <Lightbulb className="h-4 w-4 text-yellow-500" />
              <span className="text-xs font-medium text-gray-600">AI Recommendations</span>
            </div>
            <div className="space-y-2">
              {[
                `Find suppliers for ${currentSearchTerm}`,
                `Market trends for ${currentSearchTerm} in 2025`,
                `Source tactical ${currentSearchTerm}`,
                `Evaluate supplier reliability for ${currentSearchTerm}`,
                `Source waterproof ${currentSearchTerm}`
              ].map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="flex items-center w-full text-left p-2 text-xs text-gray-700 hover:bg-purple-50 hover:text-purple-700 rounded border border-gray-100 hover:border-purple-200 transition-all"
                >
                  <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Minimized Input Area */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask AI to refine search..."
            className="w-full px-3 py-2 pr-8 text-xs border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-transparent bg-white"
            disabled={isCreating || isAddingMessage}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isCreating || isAddingMessage}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
          >
            {isCreating || isAddingMessage ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Send className="h-3 w-3" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
